{"name": "KooDooMerchant", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "dev-mode": "shx cp firebaseConfigs/dev/google-services.json android/app && shx cp firebaseConfigs/dev/GoogleService-Info.plist ios && shx cp firebaseConfigs/dev/GoogleService-Info.plist ios/KooDooMerchant && shx cp constant/dev.env.js constant/env.js", "prod-mode": "shx cp firebaseConfigs/prod/google-services.json android/app && shx cp firebaseConfigs/prod/GoogleService-Info.plist ios && shx cp firebaseConfigs/prod/GoogleService-Info.plist ios/KooDooMerchant && shx cp constant/prod.env.js constant/env.js", "dev:android": "shx cp firebaseConfigs/dev/google-services.json android/app && shx cp constant/dev.env.js constant/env.js && npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output ./android/app/src/main/assets/index.android.bundle --assets-dest ./android/app/src/main/res", "prod:android": "shx cp firebaseConfigs/prod/google-services.json android/app && shx cp constant/prod.env.js constant/env.js && npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output ./android/app/src/main/assets/index.android.bundle --assets-dest ./android/app/src/main/res", "bundle:ios": "npx react-native bundle --entry-file='index.js' --bundle-output='./ios/main.jsbundle' --dev=false --platform='ios' --assets-dest='./ios'"}, "dependencies": {"@charles-johnson/react-native-ping": "1.2.15", "@conodene/react-native-thermal-receipt-printer-image-qr": "0.1.88", "@conodene/react-native-user-inactivity": "1.2.6", "@developeraspire/react-native-sunmi-barcode-scanner": "0.2.0", "@dr.pogodin/react-native-fs": "2.27.1", "@heasy/react-native-sunmi-printer": "1.8.4", "@nozbe/watermelondb": "0.27.1", "@react-native-async-storage/async-storage": "1.24.0", "@react-native-clipboard/clipboard": "1.14.1", "@react-native-community/checkbox": "0.5.17", "@react-native-community/datetimepicker": "8.2.0", "@react-native-community/netinfo": "11.3.2", "@react-native-community/push-notification-ios": "1.11.0", "@react-native-firebase/analytics": "21.14.0", "@react-native-firebase/app": "21.14.0", "@react-native-firebase/auth": "21.14.0", "@react-native-firebase/crashlytics": "21.14.0", "@react-native-firebase/database": "21.14.0", "@react-native-firebase/firestore": "21.14.0", "@react-native-firebase/messaging": "21.14.0", "@react-native-firebase/perf": "21.14.0", "@react-native-firebase/storage": "21.14.0", "@react-native-oh-tpl/react-native-tcp-socket": "6.2.0-0.0.3", "@react-native-picker/picker": "2.7.7", "@react-navigation/bottom-tabs": "6.6.1", "@react-navigation/native": "6.1.18", "@react-navigation/native-stack": "6.11.0", "@react-navigation/stack": "6.4.1", "@shopify/flash-list": "1.7.0", "@zach.codes/react-calendar": "0.3.3", "aws-sdk": "2.1659.0", "axios": "1.7.2", "base64-arraybuffer": "1.0.2", "bignumber.js": "9.1.2", "buffer": "6.0.3", "crypto-js": "4.2.0", "date-fns": "3.6.0", "deprecated-react-native-prop-types": "5.0.0", "eventemitter3": "5.0.1", "fusioncharts": "3.18.0", "hashids": "2.3.0", "iconv-lite": "0.6.3", "js-coroutines": "2.4.36", "lodash": "4.17.21", "molpay-mobile-xdk-reactnative-beta": "0.32.9", "moment": "2.30.1", "moment-timezone": "0.5.45", "nanoid": "5.0.7", "pullstate": "1.25.0", "react": "18.2.0", "react-native": "0.74.3", "react-native-background-actions": "4.0.1", "react-native-big-calendar": "4.13.2", "react-native-calendar-picker": "8.0.4", "react-native-calendars": "1.1305.0", "react-native-canvas": "0.1.40", "react-native-chart-kit": "6.12.0", "react-native-check-box": "2.1.7", "react-native-device-detection": "0.2.1", "react-native-device-info": "11.1.0", "react-native-document-picker": "9.3.0", "react-native-draggable": "3.3.0", "react-native-draggable-flatlist": "4.0.1", "react-native-dropdown-picker": "3.8.3", "react-native-external-display": "0.6.6", "react-native-fast-image": "8.6.3", "react-native-fast-text": "0.1.1", "react-native-file-logger": "0.5.5", "react-native-fusioncharts": "4.1.2", "react-native-gesture-handler": "2.17.1", "react-native-get-random-values": "1.11.0", "react-native-gifted-chat": "2.4.0", "react-native-google-places-autocomplete": "2.5.6", "react-native-html-to-pdf": "0.12.0", "react-native-image-picker": "7.1.2", "react-native-keyboard-aware-scroll-view": "0.9.5", "react-native-modal-datetime-picker": "17.1.0", "react-native-multiple-modals": "1.2.6", "react-native-orientation-locker": "1.7.0", "react-native-picker-select": "9.1.3", "react-native-printer-imin": "0.8.0", "react-native-progress-circle": "2.1.0", "react-native-push-notification": "8.1.1", "react-native-qrcode-svg": "6.3.1", "react-native-reanimated": "3.14.0", "react-native-safe-area-context": "4.10.8", "react-native-screens": "3.32.0", "react-native-simple-radio-button": "2.7.4", "react-native-smooth-picker": "1.1.5", "react-native-spin-picker": "0.0.2", "react-native-svg": "15.4.0", "react-native-switch": "1.5.1", "react-native-switch-pro": "1.0.5", "react-native-table-component": "1.2.2", "react-native-tablet-switcher": "0.2.3", "react-native-tcp-socket": "6.2.0", "react-native-text-ticker": "1.14.0", "react-native-vector-icons": "10.1.0", "react-native-view-shot": "^3.8.0", "react-native-walkthrough-tooltip": "1.6.0", "react-native-webview": "13.10.5", "react-native-worklets-core": "1.5.0", "react-native-zip-archive": "^7.0.1", "rn-fetch-blob": "0.12.0", "rn-qr-generator": "1.4.0", "string-pixel-width": "1.11.0", "xlsx": "0.18.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.74.85", "@react-native/eslint-config": "0.74.85", "@react-native/metro-config": "0.74.85", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "babel-plugin-transform-remove-console": "^6.9.4", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "18.2.0"}, "engines": {"node": ">=18"}, "overrides": {"react-native": "0.74.3"}}